import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, ActivityIndicator, Alert, StatusBar, Text, TouchableOpacity, StyleSheet, Animated, Platform, AppState, Modal, FlatList } from 'react-native';
import { WebView } from 'react-native-webview';
import Slider from '@react-native-community/slider';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useIsFocused } from '@react-navigation/native';
import * as NavigationBar from 'expo-navigation-bar';

import { slothStyles as baseSlothStyles, SLOTH_COLORS } from '../styles/sloth';
import soraApi from '../services/soraApi';
import { PROXY_BASE_URL } from '../utils/constants';

// FIX: Updated the time formatting function to handle hours.
const formatTime = (timeInSeconds) => {
    if (isNaN(timeInSeconds) || timeInSeconds < 0) {
        return '00:00';
    }

    // Calculate hours, minutes, and seconds
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    const seconds = Math.floor(timeInSeconds % 60);

    // Pad minutes and seconds with a leading zero if they are less than 10
    const paddedMinutes = minutes.toString().padStart(2, '0');
    const paddedSeconds = seconds.toString().padStart(2, '0');

    // Only include hours in the string if the duration is an hour or more
    if (hours > 0) {
        return `${hours}:${paddedMinutes}:${paddedSeconds}`;
    } else {
        return `${paddedMinutes}:${paddedSeconds}`;
    }
};

const generatePlayerHTML = (proxiedVideoUrl, subtitles = []) => `
  <!DOCTYPE html>
  <html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <style>
      body, html { margin: 0; padding: 0; width: 100%; height: 100%; background-color: #000; overflow: hidden; }
      video { width: 100%; height: 100%; }
      video::cue {
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        font-size: 18px;
        font-family: Arial, sans-serif;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.8);
      }
    </style>
  </head>
  <body>
    <video id="player" autoplay playsinline crossorigin="anonymous">
      <source src="${proxiedVideoUrl}" type="video/mp4">
      ${subtitles.map((sub, index) =>
        `<track kind="subtitles" src="${sub.url}" srclang="${sub.lang || 'en'}" label="${sub.label || sub.lang || `Subtitle ${index + 1}`}" ${index === 0 ? 'default' : ''}>`
      ).join('')}
    </video>
    <script>
      const video = document.getElementById('player');
      const postMessage = (data) => window.ReactNativeWebView.postMessage(JSON.stringify(data));

      const log = (message, data) => postMessage({ type: 'DEBUG', payload: { message, data: data || null } });

      video.addEventListener('loadedmetadata', () => {
          postMessage({ type: 'LOADED', payload: { duration: video.duration } });
          log('Video metadata loaded.', { duration: video.duration, tracks: video.textTracks.length });
      });
      video.addEventListener('play', () => postMessage({ type: 'PLAYING' }));
      video.addEventListener('pause', () => postMessage({ type: 'PAUSED' }));
      video.addEventListener('waiting', () => postMessage({ type: 'BUFFERING' }));
      video.addEventListener('playing', () => postMessage({ type: 'PLAYING' }));
      video.addEventListener('error', () => postMessage({ type: 'ERROR', payload: { error: video.error ? video.error.message : 'Unknown Video Error' } }));
      video.addEventListener('timeupdate', () => postMessage({ type: 'STATUS_UPDATE', payload: { currentTime: video.currentTime } }));

      window.handlePlayerCommand = (command, data) => {
        switch(command) {
          case 'PLAY': video.play(); break;
          case 'PAUSE': video.pause(); break;
          case 'SEEK': video.currentTime = data.time; break;
          case 'SEEK_BY': video.currentTime += data.amount; break;
          case 'CHANGE_SOURCE':
            const currentTime = video.currentTime;
            video.src = data.url;
            video.load();
            video.addEventListener('loadedmetadata', () => {
              video.currentTime = currentTime;
              video.play();
            }, { once: true });
            break;
          case 'TOGGLE_SUBTITLES':
            const tracks = video.textTracks;
            for (let i = 0; i < tracks.length; i++) {
              tracks[i].mode = i === data.index ? 'showing' : 'hidden';
            }
            break;
        }
      };
    </script>
  </body>
  </html>`;

const PlayerScreen = ({ route, navigation }) => {
    const { item, mediaType } = route.params;
    const insets = useSafeAreaInsets();
    const webviewRef = useRef(null);
    const isFocused = useIsFocused();

    const [playerHtml, setPlayerHtml] = useState(null);
    const [isPlayerReady, setIsPlayerReady] = useState(false);
    const [isVideoLoading, setIsVideoLoading] = useState(true);
    const [error, setError] = useState(null);
    const [controlsVisible, setControlsVisible] = useState(true);
    const [isPlaying, setIsPlaying] = useState(false);
    const [duration, setDuration] = useState(0);
    const [currentTime, setCurrentTime] = useState(0);

    const [availableStreams, setAvailableStreams] = useState([]);
    const [currentStreamIndex, setCurrentStreamIndex] = useState(0);
    const [availableSubtitles, setAvailableSubtitles] = useState([]);
    const [currentSubtitleIndex, setCurrentSubtitleIndex] = useState(-1);
    const [showQualityModal, setShowQualityModal] = useState(false);
    const [showSubtitleModal, setShowSubtitleModal] = useState(false);

    const controlsOpacity = useRef(new Animated.Value(1)).current;
    const controlsTimeout = useRef(null);
    const API_BASE_URL = 'https://sorastream-five.vercel.app';

    // Immersive Mode Logic
    const applyImmersiveMode = async () => {
        if (Platform.OS === 'android') {
            StatusBar.setHidden(true, 'fade');
            try {
                await NavigationBar.setVisibilityAsync('hidden');
                await NavigationBar.setBehaviorAsync('overlay-swipe');
            } catch (error) {
                console.error('Error applying immersive mode:', error);
            }
        }
    };

    const exitImmersiveMode = async () => {
        if (Platform.OS === 'android') {
            StatusBar.setHidden(false, 'fade');
            try {
                await NavigationBar.setVisibilityAsync('visible');
            } catch (error) {
                console.error('Error exiting immersive mode:', error);
            }
        }
    };

    useEffect(() => {
        if (isFocused) {
            applyImmersiveMode();
        } else {
            exitImmersiveMode();
        }
        return () => exitImmersiveMode();
    }, [isFocused]);

    useEffect(() => {
        if (isFocused && !controlsVisible && isPlaying) {
            const timer = setTimeout(() => applyImmersiveMode(), 300);
            return () => clearTimeout(timer);
        }
    }, [controlsVisible, isPlaying, isFocused]);

    useEffect(() => {
        const handleAppStateChange = (nextAppState) => {
            if (nextAppState === 'active' && isFocused && !controlsVisible && isPlaying) {
                setTimeout(() => applyImmersiveMode(), 100);
            }
        };
        const subscription = AppState.addEventListener('change', handleAppStateChange);
        return () => subscription?.remove();
    }, [isFocused, controlsVisible, isPlaying]);

    useEffect(() => {
        let interval;
        if (isFocused && !controlsVisible && isPlaying) {
            interval = setInterval(() => {
                applyImmersiveMode();
            }, 5000);
        }
        return () => {
            if (interval) clearInterval(interval);
        };
    }, [isFocused, controlsVisible, isPlaying]);
    // End Immersive Mode Logic

    useEffect(() => {
        const preparePlayer = async () => {
            try {
                const streamData = mediaType === 'movie'
                    ? await soraApi.getMovieStreams(item.id)
                    : await soraApi.getTVStreams(item.id, route.params.season.season_number, route.params.episode.episode_number);

                const processedData = soraApi.processStreamResponse(streamData);

                if (processedData && processedData.streams && processedData.streams.length > 0) {
                    setAvailableStreams(processedData.streams);
                    setAvailableSubtitles(processedData.subtitles || []);

                    console.log('Available streams:', processedData.streams.length);
                    console.log('Available subtitles:', (processedData.subtitles || []).length);

                    const bestStream = soraApi.getBestQualityStream(processedData);
                    const proxiedStreamUrl = `${PROXY_BASE_URL}${encodeURIComponent(bestStream.url)}`;

                    const fullUrlSubtitles = (processedData.subtitles || []).map(sub => ({
                        ...sub,
                        url: sub.url ? `${API_BASE_URL}${sub.url}` : null,
                        label: sub.lang
                    })).filter(sub => sub.url);

                    console.log('Processed Subtitle URLs:', fullUrlSubtitles.map(s => s.url));

                    setPlayerHtml(generatePlayerHTML(proxiedStreamUrl, fullUrlSubtitles));
                } else {
                    throw new Error('No playable stream was found.');
                }
            } catch (e) {
                setError(e.message);
                Alert.alert("Playback Error", `Could not prepare the video: ${e.message}`, [{ text: "OK", onPress: () => navigation.goBack() }]);
            }
        };
        preparePlayer();
    }, [item.id, mediaType, route.params.season, route.params.episode]);

    const showControls = useCallback((autoHide = true) => {
        clearTimeout(controlsTimeout.current);
        Animated.timing(controlsOpacity, { toValue: 1, duration: 200, useNativeDriver: true }).start();
        setControlsVisible(true);
        if (autoHide && isPlaying) {
            controlsTimeout.current = setTimeout(hideControls, 4000);
        }
    }, [isPlaying, controlsOpacity]);

    const hideControls = useCallback(() => {
        clearTimeout(controlsTimeout.current);
        Animated.timing(controlsOpacity, { toValue: 0, duration: 200, useNativeDriver: true }).start(() => {
            setControlsVisible(false);
            if (isFocused && isPlaying) {
                setTimeout(() => applyImmersiveMode(), 100);
            }
        });
    }, [isFocused, isPlaying, controlsOpacity]);

    const handleScreenTap = useCallback(() => controlsVisible ? hideControls() : showControls(), [controlsVisible, hideControls, showControls]);

    const sendCommand = (command, data = null) =>
        webviewRef.current?.injectJavaScript(`window.handlePlayerCommand('${command}', ${JSON.stringify(data)}); true;`);

    const switchQuality = useCallback((streamIndex) => {
        if (streamIndex >= 0 && streamIndex < availableStreams.length) {
            const selectedStream = availableStreams[streamIndex];
            const proxiedStreamUrl = `${PROXY_BASE_URL}${encodeURIComponent(selectedStream.url)}`;
            sendCommand('CHANGE_SOURCE', { url: proxiedStreamUrl });
            setCurrentStreamIndex(streamIndex);
            setShowQualityModal(false);
        }
    }, [availableStreams]);

    const switchSubtitle = useCallback((subtitleIndex) => {
        sendCommand('TOGGLE_SUBTITLES', { index: subtitleIndex });
        setCurrentSubtitleIndex(subtitleIndex);
        setShowSubtitleModal(false);
    }, []);

    const handleMessage = (event) => {
        try {
            const msg = JSON.parse(event.nativeEvent.data);
            switch (msg.type) {
                case 'LOADED':
                    if (!isPlayerReady) setIsPlayerReady(true);
                    setIsVideoLoading(false);
                    setDuration(msg.payload.duration);
                    showControls();
                    break;
                case 'PLAYING':
                    if (!isPlayerReady) setIsPlayerReady(true);
                    setIsVideoLoading(false);
                    setIsPlaying(true);
                    showControls();
                    break;
                case 'PAUSED':
                    setIsPlaying(false);
                    showControls(false);
                    break;
                case 'BUFFERING':
                    setIsVideoLoading(true);
                    break;
                case 'STATUS_UPDATE':
                    setCurrentTime(msg.payload.currentTime);
                    break;
                case 'ERROR':
                    setError(msg.payload.error);
                    Alert.alert("Video Playback Error", msg.payload.error);
                    break;
                case 'DEBUG':
                    console.log('[WebView DEBUG]', msg.payload.message, msg.payload.data || '');
                    break;
            }
        } catch (e) {
            console.error('Failed to parse message from WebView', e);
        }
    };

    if (!playerHtml || error) {
        return (
            <View style={slothStyles.playerLoadingContainer}>
                {error ? <Text style={slothStyles.errorText}>{error}</Text> : <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />}
            </View>
        );
    }

    return (
        <View style={slothStyles.playerContainer}>
            <WebView
                ref={webviewRef}
                style={slothStyles.playerWebView}
                source={{ html: playerHtml, baseUrl: '' }}
                onMessage={handleMessage}
                onError={(e) => setError(e.nativeEvent.description)}
                allowsFullscreenVideo
                mediaPlaybackRequiresUserAction={false}
                originWhitelist={['*']}
            />

            {!isPlayerReady && (
                <View style={slothStyles.playerLoadingContainer}>
                    <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
                </View>
            )}

            {isPlayerReady && (
                <TouchableOpacity style={StyleSheet.absoluteFill} activeOpacity={1} onPress={handleScreenTap}>
                    {controlsVisible && (
                        <Animated.View style={[slothStyles.controlsOverlay, { opacity: controlsOpacity }]}>
                            <View style={[slothStyles.topControls, { marginTop: insets.top }]}>
                               <TouchableOpacity style={slothStyles.iconButton} onPress={() => navigation.goBack()}><Ionicons name="arrow-back" size={26} color="#FFF" /></TouchableOpacity>
                               <View style={slothStyles.topRightControls}>
                                   {availableStreams.length > 1 && (
                                       <TouchableOpacity style={slothStyles.iconButton} onPress={() => setShowQualityModal(true)}>
                                           <Ionicons name="settings" size={24} color="#FFF" />
                                       </TouchableOpacity>
                                   )}
                                   {availableSubtitles.length > 0 && (
                                       <TouchableOpacity style={slothStyles.iconButton} onPress={() => setShowSubtitleModal(true)}>
                                           <Ionicons name="chatbox" size={24} color="#FFF" />
                                       </TouchableOpacity>
                                   )}
                               </View>
                            </View>
                            <View style={slothStyles.middleControls}>
                                {!isVideoLoading && <TouchableOpacity onPress={() => sendCommand('SEEK_BY', { amount: -10 })} style={slothStyles.seekButton}><Ionicons name="play-back" size={32} color="#FFF" /></TouchableOpacity>}
                                {isVideoLoading ? (
                                    <ActivityIndicator size="large" color="#FFF"/>
                                ) : (
                                    <TouchableOpacity onPress={() => sendCommand(isPlaying ? 'PAUSE' : 'PLAY')}><Ionicons name={isPlaying ? 'pause-circle' : 'play-circle'} size={70} color="#FFF" /></TouchableOpacity>
                                )}
                                {!isVideoLoading && <TouchableOpacity onPress={() => sendCommand('SEEK_BY', { amount: 10 })} style={slothStyles.seekButton}><Ionicons name="play-forward" size={32} color="#FFF" /></TouchableOpacity>}
                            </View>
                            <View style={[slothStyles.bottomControls, { marginBottom: insets.bottom || 10 }]}>
                               <View style={slothStyles.timeRow}><Text style={slothStyles.timeText}>{formatTime(currentTime)}</Text><Text style={slothStyles.timeText}>{formatTime(duration)}</Text></View>
                               <Slider
                                   style={{width: '100%', height: 40}}
                                   value={currentTime}
                                   maximumValue={duration || 1}
                                   onSlidingComplete={(value) => sendCommand('SEEK', {time: value})}
                                   minimumTrackTintColor={SLOTH_COLORS.primary}
                                   maximumTrackTintColor="rgba(255,255,255,0.5)"
                                   thumbTintColor={SLOTH_COLORS.primary}
                               />
                            </View>
                        </Animated.View>
                    )}
                </TouchableOpacity>
            )}

            <Modal animationType="slide" transparent={true} visible={showQualityModal} onRequestClose={() => setShowQualityModal(false)}>
                <TouchableOpacity style={slothStyles.modalOverlay} activeOpacity={1} onPress={() => setShowQualityModal(false)}>
                    <View style={[slothStyles.modalContent, { marginBottom: insets.bottom }]}>
                        <Text style={slothStyles.modalHeader}>Select Quality</Text>
                        <FlatList
                            data={availableStreams}
                            keyExtractor={(_, index) => index.toString()}
                            renderItem={({ item: stream, index }) => (
                                <TouchableOpacity
                                    style={[slothStyles.modalItem, currentStreamIndex === index && { backgroundColor: 'rgba(94, 56, 244, 0.2)' }]}
                                    onPress={() => switchQuality(index)}
                                >
                                    <Text style={[slothStyles.modalItemText, currentStreamIndex === index && { color: SLOTH_COLORS.primary }]}>
                                        {stream.name || stream.quality || 'Unknown Quality'}
                                    </Text>
                                </TouchableOpacity>
                            )}
                        />
                    </View>
                </TouchableOpacity>
            </Modal>

            <Modal animationType="slide" transparent={true} visible={showSubtitleModal} onRequestClose={() => setShowSubtitleModal(false)}>
                <TouchableOpacity style={slothStyles.modalOverlay} activeOpacity={1} onPress={() => setShowSubtitleModal(false)}>
                    <View style={[slothStyles.modalContent, { marginBottom: insets.bottom }]}>
                        <Text style={slothStyles.modalHeader}>Select Subtitles</Text>
                        <FlatList
                            data={[{ label: 'Off', lang: 'off' }, ...availableSubtitles]}
                            keyExtractor={(_, index) => index.toString()}
                            renderItem={({ item: subtitle, index }) => {
                                const subtitleIndex = index - 1;
                                return (
                                    <TouchableOpacity
                                        style={[slothStyles.modalItem, currentSubtitleIndex === subtitleIndex && { backgroundColor: 'rgba(94, 56, 244, 0.2)' }]}
                                        onPress={() => switchSubtitle(subtitleIndex)}
                                    >
                                        <Text style={[slothStyles.modalItemText, currentSubtitleIndex === subtitleIndex && { color: SLOTH_COLORS.primary }]}>
                                            {subtitle.label || subtitle.lang || `Subtitle ${index}`}
                                        </Text>
                                    </TouchableOpacity>
                                );
                            }}
                        />
                    </View>
                </TouchableOpacity>
            </Modal>
        </View>
    );
};

const slothStyles = StyleSheet.create({
    ...baseSlothStyles,
    playerContainer: { flex: 1, backgroundColor: '#000' },
    playerLoadingContainer: { ...StyleSheet.absoluteFillObject, backgroundColor: '#000', justifyContent: 'center', alignItems: 'center', zIndex: 10 },
    playerWebView: { flex: 1, backgroundColor: '#000' },
    controlsOverlay: { ...StyleSheet.absoluteFillObject, backgroundColor: 'rgba(0,0,0,0.3)', justifyContent: 'space-between' },
    topControls: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 10 },
    topRightControls: { flexDirection: 'row', },
    middleControls: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-evenly' },
    bottomControls: { paddingHorizontal: 20 },
    iconButton: { padding: 10, borderRadius: 25, backgroundColor: 'rgba(0,0,0,0.4)', marginLeft: 10 },
    seekButton: { padding: 20 },
    timeRow: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: -10 },
    timeText: { color: '#FFF', fontSize: 12, fontWeight: 'bold', textShadowColor: 'rgba(0,0,0,0.7)', textShadowOffset: { width: 1, height: 1 }, textShadowRadius: 3 },
    errorText: { color: SLOTH_COLORS.primary, padding: 20, textAlign: 'center' },
    modalOverlay: { flex: 1, justifyContent: 'flex-end', backgroundColor: 'rgba(0,0,0,0.6)' },
    modalContent: { backgroundColor: '#212121', borderTopRightRadius: 20, borderTopLeftRadius: 20, padding: 20 },
    modalHeader: { color: '#FFF', fontSize: 18, fontWeight: 'bold', marginBottom: 15, textAlign: 'center' },
    modalItem: { paddingVertical: 15, borderBottomWidth: 1, borderBottomColor: '#424242' },
    modalItemText: { color: '#FFF', fontSize: 16, textAlign: 'center' }
});

export default PlayerScreen;